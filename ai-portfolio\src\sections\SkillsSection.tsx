import React from "react";
import { motion } from "framer-motion";
import { FlowingMenu } from "../components/FlowingMenu";

const skills = [
  { name: "Python", level: 95, icon: "🐍", color: "#3776ab" },
  { name: "TensorFlow", level: 90, icon: "🧠", color: "#ff6f00" },
  { name: "PyTorch", level: 88, icon: "🔥", color: "#ee4c2c" },
  { name: "React", level: 92, icon: "⚛️", color: "#61dafb" },
  { name: "Node.js", level: 85, icon: "🟢", color: "#339933" },
  { name: "Docker", level: 82, icon: "🐳", color: "#2496ed" },
  { name: "Kubernet<PERSON>", level: 78, icon: "☸️", color: "#326ce5" },
  { name: "AWS", level: 80, icon: "☁️", color: "#ff9900" },
  { name: "MongoDB", level: 83, icon: "🍃", color: "#47a248" },
  { name: "PostgreSQL", level: 87, icon: "🐘", color: "#336791" },
  { name: "OpenCV", level: 85, icon: "👁️", color: "#5c3ee8" },
  { name: "Scikit-learn", level: 90, icon: "📊", color: "#f7931e" },
  { name: "FastAPI", level: 88, icon: "⚡", color: "#009688" },
  { name: "GraphQL", level: 75, icon: "📈", color: "#e10098" },
  { name: "Redis", level: 80, icon: "🔴", color: "#dc382d" },
  { name: "Git", level: 93, icon: "📝", color: "#f05032" },
  { name: "Jupyter", level: 89, icon: "📓", color: "#f37626" },
  { name: "MLflow", level: 82, icon: "🔄", color: "#0194e2" },
];

export const SkillsSection: React.FC = () => {
  return (
    <section id="skills" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Technical <span className="text-gradient">Skills</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            A comprehensive toolkit for building intelligent and scalable
            solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
          {/* Languages */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                <span className="text-white">🔵</span>
              </div>
              <h3 className="text-xl font-bold text-white">Languages</h3>
            </div>
            <div className="flex flex-wrap gap-3">
              {skills.filter(skill => ["Python", "JavaScript", "TypeScript"].includes(skill.name)).map((skill) => (
                <motion.div
                  key={skill.name}
                  className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-blue-700/30 text-blue-300 rounded-full border border-blue-500/30 text-sm"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)"
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {skill.icon} {skill.name}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Tools */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span className="text-white">🟢</span>
              </div>
              <h3 className="text-xl font-bold text-white">Tools</h3>
            </div>
            <div className="flex flex-wrap gap-3">
              {skills.filter(skill => ["TensorFlow", "PyTorch", "Docker", "Kubernetes", "Git", "MLflow"].includes(skill.name)).map((skill) => (
                <motion.div
                  key={skill.name}
                  className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-green-700/30 text-green-300 rounded-full border border-green-500/30 text-sm"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 0 15px rgba(16, 185, 129, 0.5)"
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {skill.icon} {skill.name}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Platforms */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                <span className="text-white">🟣</span>
              </div>
              <h3 className="text-xl font-bold text-white">Platforms</h3>
            </div>
            <div className="flex flex-wrap gap-3">
              {skills.filter(skill => ["AWS", "React", "Node.js", "FastAPI", "MongoDB", "PostgreSQL", "Redis"].includes(skill.name)).map((skill) => (
                <motion.div
                  key={skill.name}
                  className="px-4 py-2 bg-gradient-to-r from-purple-500/20 to-purple-700/30 text-purple-300 rounded-full border border-purple-500/30 text-sm"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 0 15px rgba(139, 92, 246, 0.5)"
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {skill.icon} {skill.name}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="card-bg rounded-xl p-6 text-center">
            <div className="text-3xl mb-4">🤖</div>
            <h3 className="text-xl font-bold text-white mb-2">
              AI & Machine Learning
            </h3>
            <p className="text-gray-300 text-sm">
              Deep expertise in neural networks, computer vision, NLP, and MLOps
            </p>
          </div>

          <div className="card-bg rounded-xl p-6 text-center">
            <div className="text-3xl mb-4">💻</div>
            <h3 className="text-xl font-bold text-white mb-2">
              Full-Stack Development
            </h3>
            <p className="text-gray-300 text-sm">
              Modern web technologies, APIs, databases, and cloud infrastructure
            </p>
          </div>

          <div className="card-bg rounded-xl p-6 text-center">
            <div className="text-3xl mb-4">📊</div>
            <h3 className="text-xl font-bold text-white mb-2">Data Science</h3>
            <p className="text-gray-300 text-sm">
              Statistical analysis, data visualization, and predictive modeling
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
