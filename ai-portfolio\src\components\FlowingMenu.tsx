import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "../utils/cn";

interface Skill {
  name: string;
  level: number;
  icon?: string;
  color?: string;
}

interface FlowingMenuProps {
  skills: Skill[];
  className?: string;
}

export const FlowingMenu: React.FC<FlowingMenuProps> = ({
  skills,
  className = "",
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.8,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20,
      },
    },
  };

  const getSkillColor = (level: number) => {
    if (level >= 90) return "from-green-400 to-emerald-600";
    if (level >= 75) return "from-blue-400 to-cyan-600";
    if (level >= 60) return "from-yellow-400 to-orange-600";
    return "from-red-400 to-pink-600";
  };

  return (
    <motion.div
      className={cn(
        "flex flex-wrap justify-center gap-4 max-w-4xl mx-auto",
        className,
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {skills.map((skill, index) => (
        <motion.div
          key={skill.name}
          className="relative"
          variants={itemVariants}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <motion.div
            className={cn(
              "card-bg rounded-full px-6 py-3 cursor-pointer transition-all duration-300",
              "hover:shadow-lg hover:shadow-blue-500/25",
            )}
            whileHover={{
              scale: 1.1,
              rotateZ: Math.random() * 10 - 5,
            }}
            whileTap={{ scale: 0.95 }}
            style={{
              background:
                hoveredIndex === index
                  ? `linear-gradient(135deg, ${skill.color || "#3b82f6"}, ${skill.color || "#8b5cf6"})`
                  : undefined,
            }}
          >
            <div className="flex items-center space-x-2">
              {skill.icon && <span className="text-lg">{skill.icon}</span>}
              <span className="text-white font-medium text-sm">
                {skill.name}
              </span>
            </div>

            <AnimatePresence>
              {hoveredIndex === index && (
                <motion.div
                  className="absolute -top-12 left-1/2 transform -translate-x-1/2 card-bg rounded-lg px-3 py-2 text-xs text-white whitespace-nowrap"
                  initial={{ opacity: 0, y: 10, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex items-center space-x-2">
                    <span>Proficiency:</span>
                    <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className={`h-full bg-gradient-to-r ${getSkillColor(skill.level)}`}
                        initial={{ width: 0 }}
                        animate={{ width: `${skill.level}%` }}
                        transition={{ duration: 0.5, delay: 0.1 }}
                      />
                    </div>
                    <span>{skill.level}%</span>
                  </div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white/20" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Floating animation */}
          <motion.div
            className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 -z-10"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        </motion.div>
      ))}
    </motion.div>
  );
};
