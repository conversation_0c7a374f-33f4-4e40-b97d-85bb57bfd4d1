{"name": "autoprefixer", "version": "10.4.0", "description": "Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website", "engines": {"node": "^10 || ^12 || >=14"}, "keywords": ["autoprefixer", "css", "prefix", "postcss", "postcss-plugin"], "main": "lib/autoprefixer.js", "bin": "bin/autoprefixer", "types": "lib/autoprefixer.d.ts", "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "postcss/autoprefixer", "bugs": {"url": "https://github.com/postcss/autoprefixer/issues"}, "peerDependencies": {"postcss": "^8.1.0"}, "dependencies": {"browserslist": "^4.17.5", "caniuse-lite": "^1.0.30001272", "fraction.js": "^4.1.1", "normalize-range": "^0.1.2", "picocolors": "^1.0.0", "postcss-value-parser": "^4.1.0"}}