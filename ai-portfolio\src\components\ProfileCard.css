:root {
  --pointer-x: 50%;
  --pointer-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0.5;
  --pointer-from-left: 0.5;
  --card-opacity: 0;
  --rotate-x: 0deg;
  --rotate-y: 0deg;
  --background-x: 50%;
  --background-y: 50%;
  --grain: none;
  --icon: none;
  --behind-gradient: none;
  --inner-gradient: none;
  --sunpillar-1: hsl(2, 100%, 73%);
  --sunpillar-2: hsl(53, 100%, 69%);
  --sunpillar-3: hsl(93, 100%, 69%);
  --sunpillar-4: hsl(176, 100%, 76%);
  --sunpillar-5: hsl(228, 100%, 74%);
  --sunpillar-6: hsl(283, 100%, 73%);
  --sunpillar-clr-1: var(--sunpillar-1);
  --sunpillar-clr-2: var(--sunpillar-2);
  --sunpillar-clr-3: var(--sunpillar-3);
}

.pc-card-wrapper {
  perspective: 1000px;
  width: 100%;
  max-width: 400px;
  height: 500px;
  position: relative;
}

.pc-card {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  transform: rotateX(var(--rotate-x)) rotateY(var(--rotate-y));
}

.pc-card.active {
  --card-opacity: 1;
}

.pc-inside {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  background: var(--inner-gradient);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pc-inside::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--behind-gradient);
  opacity: var(--card-opacity);
  transition: opacity 0.6s ease;
  pointer-events: none;
}

.pc-shine {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  opacity: calc(var(--pointer-from-center) * 0.5);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pc-glare {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at var(--pointer-x) var(--pointer-y),
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%
  );
  opacity: calc(var(--pointer-from-center) * 0.8);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pc-content {
  position: relative;
  z-index: 10;
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pc-avatar-content {
  justify-content: center;
  align-items: center;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.pc-user-info {
  width: 100%;
  margin-top: 2rem;
}

.pc-user-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.pc-mini-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.pc-mini-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pc-user-text {
  flex: 1;
  margin-left: 1rem;
  text-align: left;
}

.pc-handle {
  color: #a855f7;
  font-weight: 600;
  font-size: 0.9rem;
}

.pc-status {
  color: #10b981;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.pc-contact-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pc-contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.pc-details {
  text-align: center;
  color: white;
}

.pc-details h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pc-details p {
  color: #d1d5db;
  font-size: 1rem;
  margin: 0;
}
