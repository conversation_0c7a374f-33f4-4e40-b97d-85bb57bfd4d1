import React from "react";
import { motion } from "framer-motion";
import Iridescence from "../components/Iridescence";
import { <PERSON>edin, Github, Twitter, Mail } from "lucide-react";

export const FooterSection: React.FC = () => {
  return (
    <footer id="contact" className="relative">
      <Iridescence
        className="py-20 px-6"
        color={[1, 1, 1]}
        mouseReact={true}
        amplitude={0.1}
        speed={1.0}
      >
        <div className="max-w-4xl mx-auto text-center space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-4"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Wanna create
              <br />
              something awesome
              <br />
              together?
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Let's collaborate on your next AI project and bring innovative
              ideas to life
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <motion.button
              className="px-12 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium text-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                window.location.href = "mailto:<EMAIL>";
              }}
            >
              Let's Talk
            </motion.button>
          </motion.div>

          <motion.div
            className="pt-12 border-t border-white/20"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col items-center space-y-6">
              <div className="flex space-x-6">
                {[
                  { name: "LinkedIn", url: "https://linkedin.com", icon: <Linkedin className="w-5 h-5" /> },
                  { name: "GitHub", url: "https://github.com", icon: <Github className="w-5 h-5" /> },
                  { name: "Twitter", url: "https://twitter.com", icon: <Twitter className="w-5 h-5" /> },
                  { name: "Email", url: "mailto:<EMAIL>", icon: <Mail className="w-5 h-5" /> },
                ].map((link, index) => (
                  <motion.a
                    key={link.name}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center text-white hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
                    whileHover={{
                      scale: 1.1,
                      background: "linear-gradient(135deg, #3b82f6, #8b5cf6)"
                    }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {link.icon}
                  </motion.a>
                ))}
              </div>
              
              <div className="text-gray-300 text-sm">
                © 2024 Alex Johnson. All rights reserved.
              </div>
            </div>
          </motion.div>
        </div>
      </Iridescence>
    </footer>
  );
};
