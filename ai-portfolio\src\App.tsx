import { Frame } from "./components/Frame";
import { GooeyNav } from "./components/GooeyNav";
import { HeroSection } from "./sections/HeroSection";
import { AboutSection } from "./sections/AboutSection";
import { ProjectsSection } from "./sections/ProjectsSection";
import { SkillsSection } from "./sections/SkillsSection";
import { FooterSection } from "./sections/FooterSection";

function App() {
  return (
    <div className="relative" data-oid="yy2.err">
      {/* Navigation */}
      <GooeyNav data-oid="3p0iezc" />

      {/* Main Content with Frame */}
      <Frame data-oid="o:pi96u">
        <HeroSection data-oid="0ap3c9m" />
        <AboutSection data-oid="a1q.-v:" />
        <ProjectsSection data-oid="_z-8uod" />
        <SkillsSection data-oid="2mmypru" />
        <FooterSection data-oid="p_bsj_b" />
      </Frame>
    </div>
  );
}

export default App;
