import React from 'react';
import { cn } from '../utils/cn';

interface IridescenceProps {
  className?: string;
  children?: React.ReactNode;
}

export const Iridescence: React.FC<IridescenceProps> = ({
  className = '',
  children,
}) => {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-teal-900/20" />
      <div className="absolute inset-0 bg-gradient-to-tr from-pink-500/10 via-purple-500/10 to-cyan-500/10 animate-pulse" />
      <div 
        className="absolute inset-0 opacity-30"
        style={{
          background: `
            radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)
          `,
          animation: 'float 8s ease-in-out infinite',
        }}
      />
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          background: `
            conic-gradient(from 0deg at 50% 50%, 
              rgba(255, 0, 150, 0.1) 0deg,
              rgba(0, 255, 255, 0.1) 60deg,
              rgba(255, 255, 0, 0.1) 120deg,
              rgba(255, 0, 150, 0.1) 180deg,
              rgba(0, 255, 255, 0.1) 240deg,
              rgba(255, 255, 0, 0.1) 300deg,
              rgba(255, 0, 150, 0.1) 360deg
            )
          `,
          animation: 'spin 20s linear infinite',
        }}
      />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};
