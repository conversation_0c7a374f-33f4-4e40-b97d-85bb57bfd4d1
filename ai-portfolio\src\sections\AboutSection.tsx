import React from "react";
import { motion } from "framer-motion";
import ProfileCard from "../components/ProfileCard";
import {
  Brain,
  C<PERSON>,
  MessageSquare,
  Eye,
  BarChart2,
  <PERSON>tings,
  FlaskConical,
  Code2
} from "lucide-react";

export const AboutSection: React.FC = () => {
  return (
    <section id="about" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            About <span className="text-gradient">Me</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Passionate about creating intelligent solutions that make a
            difference
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* About Me Content */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-white">Experience</h3>
              <p className="text-gray-300 leading-relaxed">
                With over 5 years of experience in AI development and machine learning,
                I specialize in creating innovative solutions that bridge the gap between
                complex algorithms and practical applications.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-white">Approach</h3>
              <p className="text-gray-300 leading-relaxed">
                My passion lies in developing intelligent systems that can understand,
                learn, and adapt to solve real-world problems. I've worked with leading
                tech companies and startups, delivering cutting-edge AI solutions in
                natural language processing, computer vision, and predictive analytics.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-white">What I Do</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[
                  {skill: "Machine Learning", icon: <Brain className="w-4 h-4 text-blue-400" />},
                  {skill: "Deep Learning", icon: <Cpu className="w-4 h-4 text-blue-400" />},
                  {skill: "Natural Language Processing", icon: <MessageSquare className="w-4 h-4 text-blue-400" />},
                  {skill: "Computer Vision", icon: <Eye className="w-4 h-4 text-blue-400" />},
                  {skill: "Data Science", icon: <BarChart2 className="w-4 h-4 text-blue-400" />},
                  {skill: "MLOps & Deployment", icon: <Settings className="w-4 h-4 text-blue-400" />},
                  {skill: "AI Research", icon: <FlaskConical className="w-4 h-4 text-blue-400" />},
                  {skill: "Full-Stack Development", icon: <Code2 className="w-4 h-4 text-blue-400" />},
                ].map((item, index) => (
                  <motion.div
                    key={item.skill}
                    className="flex items-center space-x-2"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {item.icon}
                    <span className="text-gray-300">{item.skill}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-white">Philosophy</h3>
              <p className="text-gray-300 leading-relaxed">
                I believe the best AI solutions enhance human capabilities rather than
                replace them. My goal is to create technology that empowers people and
                organizations to achieve more than they thought possible.
              </p>
            </div>
          </motion.div>

          {/* Profile Card */}
          <motion.div
            className="flex items-center justify-center"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            whileHover={{ scale: 1.03 }}
            transition={{
              duration: 0.6,
              delay: 0.2,
              hover: { duration: 0.3 }
            }}
            viewport={{ once: true }}
          >
            <ProfileCard
              avatarUrl="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"
              name="Alex Johnson"
              title="AI Developer & ML Engineer"
              handle="alexdev"
              status="Available for work"
              contactText="Contact Me"
              showUserInfo={true}
              enableTilt={true}
              onContactClick={() => {
                const element = document.querySelector("#contact");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};
