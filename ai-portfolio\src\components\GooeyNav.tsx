import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Home, User, Briefcase, Code, Mail } from "lucide-react";
import { cn } from "../utils/cn";

interface NavItem {
  icon: React.ReactNode;
  label: string;
  href: string;
}

const navItems: NavItem[] = [
  { icon: <Home size={20} />, label: "Home", href: "#home" },
  {
    icon: <User size={20} />,
    label: "About",
    href: "#about",
  },
  {
    icon: <Briefcase size={20} />,
    label: "Projects",
    href: "#projects",
  },
  {
    icon: <Code size={20} />,
    label: "Skills",
    href: "#skills",
  },
  {
    icon: <Mail size={20} />,
    label: "Contact",
    href: "#contact",
  },
];

interface GooeyNavProps {
  className?: string;
}

export const GooeyNav: React.FC<GooeyNavProps> = ({ className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsOpen(false);
  };

  return (
    <div className={cn("fixed top-6 right-6 z-50", className)}>
      <motion.div
        className="glass rounded-full p-2"
        style={{
          filter: "url(#gooey)",
        }}
      >
        <svg style={{ position: "absolute", width: 0, height: 0 }}>
          <defs>
            <filter id="gooey">
              <feGaussianBlur
                in="SourceGraphic"
                stdDeviation="3"
                result="blur"
              />

              <feColorMatrix
                in="blur"
                mode="matrix"
                values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -8"
                result="gooey"
              />

              <feBlend in="SourceGraphic" in2="gooey" />
            </filter>
          </defs>
        </svg>

        <motion.button
          className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white shadow-lg"
          onClick={() => setIsOpen(!isOpen)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <motion.div
            animate={{ rotate: isOpen ? 45 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="w-5 h-0.5 bg-white mb-1" />
            <div className="w-5 h-0.5 bg-white mb-1" />
            <div className="w-5 h-0.5 bg-white" />
          </motion.div>
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute top-0 right-0 flex flex-col items-end space-y-3 pt-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {navItems.map((item, index) => (
                <motion.button
                  key={item.label}
                  className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-300",
                    hoveredIndex === index
                      ? "bg-gradient-to-r from-purple-500 to-pink-500 scale-110"
                      : "bg-gradient-to-r from-blue-500/80 to-purple-600/80",
                  )}
                  onClick={() => scrollToSection(item.href)}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  initial={{ x: 100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: 100, opacity: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {item.icon}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};
