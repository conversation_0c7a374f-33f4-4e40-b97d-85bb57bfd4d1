import React from "react";
import { motion } from "framer-motion";
import { TiltedCard } from "../components/TiltedCard";

const projects = [
  {
    title: "AI Chat Assistant",
    description:
      "An intelligent conversational AI built with advanced NLP capabilities, featuring real-time responses and context awareness.",
    image:
      "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop",
    technologies: ["Python", "TensorFlow", "React", "FastAPI"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
  {
    title: "Computer Vision Platform",
    description:
      "A comprehensive platform for image recognition and analysis using state-of-the-art deep learning models.",
    image:
      "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop",
    technologies: ["PyTorch", "OpenCV", "Docker", "AWS"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
  {
    title: "ML Model Deployment",
    description:
      "Scalable machine learning model deployment system with automated CI/CD pipeline and monitoring.",
    image:
      "https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=400&h=300&fit=crop",
    technologies: ["Kubernetes", "MLflow", "Prometheus", "Grafana"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
  {
    title: "Data Analytics Dashboard",
    description:
      "Interactive dashboard for real-time data visualization and business intelligence with predictive analytics.",
    image:
      "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
    technologies: ["D3.js", "Python", "PostgreSQL", "Redis"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
  {
    title: "Neural Network Optimizer",
    description:
      "Advanced optimization algorithms for neural network training with automated hyperparameter tuning.",
    image:
      "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=400&h=300&fit=crop",
    technologies: ["PyTorch", "Optuna", "Ray", "Jupyter"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
  {
    title: "AI Code Generator",
    description:
      "Intelligent code generation tool that assists developers in writing efficient and optimized code.",
    image:
      "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop",
    technologies: ["Transformers", "React", "Node.js", "MongoDB"],
    liveUrl: "https://example.com",
    githubUrl: "https://github.com",
  },
];

export const ProjectsSection: React.FC = () => {
  return (
    <section id="projects" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Featured <span className="text-gradient">Projects</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Explore my latest work in AI development, machine learning, and
            innovative software solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <TiltedCard {...project} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
