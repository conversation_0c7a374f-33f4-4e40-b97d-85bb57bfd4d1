import React from "react";
import { motion } from "framer-motion";
import { cn } from "../utils/cn";

interface FrameProps {
  children: React.ReactNode;
  className?: string;
}

export const Frame: React.FC<FrameProps> = ({ children, className = "" }) => {
  return (
    <div className={cn("min-h-screen bg-gray-900 p-4 md:p-6", className)}>
      {/* Title in top left corner - positioned outside the frame */}
      <motion.div
        className="fixed top-6 left-6 z-[100]"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      >
        <div className="relative">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-tl-2xl rounded-br-2xl font-bold text-lg shadow-lg border border-white/20">
            <PERSON>
          </div>
          {/* Curved edge at bottom right */}
          <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-gray-900 rounded-tl-full" />
        </div>
      </motion.div>

      {/* Main content area with frame */}
      <div className="relative min-h-[calc(100vh-2rem)] md:min-h-[calc(100vh-3rem)] bg-black border-2 border-gray-700 rounded-2xl overflow-hidden shadow-2xl">
        {/* Corner decorations */}
        <div className="absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-blue-500/50 rounded-tl-2xl z-20" />

        <div className="absolute top-0 right-0 w-8 h-8 border-r-2 border-t-2 border-purple-500/50 rounded-tr-2xl z-20" />

        <div className="absolute bottom-0 left-0 w-8 h-8 border-l-2 border-b-2 border-purple-500/50 rounded-bl-2xl z-20" />

        <div className="absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-blue-500/50 rounded-br-2xl z-20" />

        {/* Animated border glow */}
        <motion.div
          className="absolute inset-0 rounded-2xl opacity-50"
          style={{
            background: `
              linear-gradient(90deg,
                rgba(59, 130, 246, 0.2) 0%,
                rgba(147, 51, 234, 0.2) 25%,
                rgba(236, 72, 153, 0.2) 50%,
                rgba(59, 130, 246, 0.2) 75%,
                rgba(147, 51, 234, 0.2) 100%
              )
            `,
            backgroundSize: "200% 100%",
          }}
          animate={{
            backgroundPosition: ["0% 0%", "200% 0%"],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* Content */}
        <div className="relative z-10 h-full">{children}</div>
      </div>
    </div>
  );
};
